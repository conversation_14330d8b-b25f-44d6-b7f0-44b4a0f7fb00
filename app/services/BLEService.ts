import { Ble<PERSON><PERSON>ger, Device, State, Subscription } from 'react-native-ble-plx';
import { PermissionsAndroid, Platform, Alert } from 'react-native';

export interface DiscoveredDevice {
  id: string;
  name: string | null;
  rssi: number;
  isConnectable: boolean;
  serviceUUIDs: string[] | null;
  manufacturerData: string | null;
  localName: string | null;
  isBrewtool: boolean;
}

export interface ConnectedDevice {
  id: string;
  name: string;
  isConnected: boolean;
  services: string[];
}

class BLEService {
  private static instance: BLEService;
  private manager: BleManager;
  private scanSubscription: Subscription | null = null;
  private discoveredDevices: Map<string, DiscoveredDevice> = new Map();
  private connectedDevices: Map<string, ConnectedDevice> = new Map();
  private scanListeners: ((devices: DiscoveredDevice[]) => void)[] = [];
  private connectionListeners: ((devices: ConnectedDevice[]) => void)[] = [];
  private isScanning = false;

  // Brewtool specific UUIDs and identifiers
  private readonly BREWTOOL_SERVICE_UUID = '6E400001-B5A3-F393-E0A9-E50E24DCCA9E';
  private readonly BREWTOOL_NAMES = ['Brewtool', 'BT-', 'BREWTOOL'];

  private constructor() {
    this.manager = new BleManager();
    this.setupBleManager();
  }

  static getInstance(): BLEService {
    if (!BLEService.instance) {
      BLEService.instance = new BLEService();
    }
    return BLEService.instance;
  }

  private setupBleManager() {
    this.manager.onStateChange((state) => {
      console.log('BLE State changed:', state);
      if (state === State.PoweredOn) {
        console.log('BLE is ready');
      }
    }, true);
  }

  // Permission handling
  async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ]);

        const allGranted = Object.values(granted).every(
          (permission) => permission === PermissionsAndroid.RESULTS.GRANTED
        );

        if (!allGranted) {
          Alert.alert(
            'Berechtigungen erforderlich',
            'Bluetooth-Berechtigungen sind erforderlich, um Geräte zu finden.'
          );
          return false;
        }
      } catch (error) {
        console.error('Permission request failed:', error);
        return false;
      }
    }
    return true;
  }

  // Check if BLE is available and enabled
  async isBluetoothEnabled(): Promise<boolean> {
    const state = await this.manager.state();
    return state === State.PoweredOn;
  }

  // Device discovery
  private isBrewtoolDevice(device: Device): boolean {
    const name = device.name || device.localName || '';
    const manufacturerData = device.manufacturerData;
    
    // Check by name
    const nameMatch = this.BREWTOOL_NAMES.some(brewtoolName => 
      name.toUpperCase().includes(brewtoolName.toUpperCase())
    );
    
    // Check by service UUID
    const serviceMatch = device.serviceUUIDs?.includes(this.BREWTOOL_SERVICE_UUID);
    
    // Check manufacturer data (if available)
    const manufacturerMatch = manufacturerData && manufacturerData.includes('Brewtool');
    
    return nameMatch || serviceMatch || manufacturerMatch;
  }

  private convertToDiscoveredDevice(device: Device): DiscoveredDevice {
    return {
      id: device.id,
      name: device.name,
      rssi: device.rssi || -100,
      isConnectable: device.isConnectable || false,
      serviceUUIDs: device.serviceUUIDs,
      manufacturerData: device.manufacturerData,
      localName: device.localName,
      isBrewtool: this.isBrewtoolDevice(device),
    };
  }

  // Start scanning for devices
  async startScan(brewToolsOnly: boolean = false): Promise<void> {
    if (this.isScanning) {
      console.log('Already scanning');
      return;
    }

    const hasPermissions = await this.requestPermissions();
    if (!hasPermissions) {
      throw new Error('Bluetooth permissions not granted');
    }

    const isEnabled = await this.isBluetoothEnabled();
    if (!isEnabled) {
      throw new Error('Bluetooth is not enabled');
    }

    this.discoveredDevices.clear();
    this.isScanning = true;

    console.log('Starting BLE scan...');

    this.scanSubscription = this.manager.startDeviceScan(
      brewToolsOnly ? [this.BREWTOOL_SERVICE_UUID] : null,
      { allowDuplicates: false },
      (error, device) => {
        if (error) {
          console.error('Scan error:', error);
          this.stopScan();
          return;
        }

        if (device) {
          const discoveredDevice = this.convertToDiscoveredDevice(device);
          
          // Filter for Brewtools if requested
          if (brewToolsOnly && !discoveredDevice.isBrewtool) {
            return;
          }

          this.discoveredDevices.set(device.id, discoveredDevice);
          this.notifyScanListeners();
        }
      }
    );

    // Auto-stop scan after 30 seconds
    setTimeout(() => {
      if (this.isScanning) {
        this.stopScan();
      }
    }, 30000);
  }

  // Stop scanning
  stopScan(): void {
    if (this.scanSubscription) {
      this.scanSubscription.remove();
      this.scanSubscription = null;
    }
    this.isScanning = false;
    console.log('BLE scan stopped');
  }

  // Get discovered devices
  getDiscoveredDevices(): DiscoveredDevice[] {
    return Array.from(this.discoveredDevices.values());
  }

  // Get only Brewtool devices
  getBrewtoolDevices(): DiscoveredDevice[] {
    return this.getDiscoveredDevices().filter(device => device.isBrewtool);
  }

  // Device connection
  async connectToDevice(deviceId: string): Promise<ConnectedDevice> {
    try {
      console.log(`Connecting to device: ${deviceId}`);
      
      const device = await this.manager.connectToDevice(deviceId);
      const deviceWithServices = await device.discoverAllServicesAndCharacteristics();
      
      const services = await deviceWithServices.services();
      const serviceUUIDs = services.map(service => service.uuid);

      const connectedDevice: ConnectedDevice = {
        id: device.id,
        name: device.name || device.localName || 'Unknown Device',
        isConnected: true,
        services: serviceUUIDs,
      };

      this.connectedDevices.set(deviceId, connectedDevice);
      this.notifyConnectionListeners();

      // Set up disconnect listener
      device.onDisconnected((error, disconnectedDevice) => {
        console.log(`Device ${disconnectedDevice?.id} disconnected`);
        this.connectedDevices.delete(deviceId);
        this.notifyConnectionListeners();
      });

      return connectedDevice;
    } catch (error) {
      console.error('Connection failed:', error);
      throw new Error(`Failed to connect to device: ${error}`);
    }
  }

  // Disconnect from device
  async disconnectFromDevice(deviceId: string): Promise<void> {
    try {
      await this.manager.cancelDeviceConnection(deviceId);
      this.connectedDevices.delete(deviceId);
      this.notifyConnectionListeners();
    } catch (error) {
      console.error('Disconnect failed:', error);
      throw new Error(`Failed to disconnect from device: ${error}`);
    }
  }

  // Get connected devices
  getConnectedDevices(): ConnectedDevice[] {
    return Array.from(this.connectedDevices.values());
  }

  // Check if device is connected
  isDeviceConnected(deviceId: string): boolean {
    return this.connectedDevices.has(deviceId);
  }

  // Listeners for scan results
  addScanListener(listener: (devices: DiscoveredDevice[]) => void): void {
    this.scanListeners.push(listener);
  }

  removeScanListener(listener: (devices: DiscoveredDevice[]) => void): void {
    const index = this.scanListeners.indexOf(listener);
    if (index > -1) {
      this.scanListeners.splice(index, 1);
    }
  }

  private notifyScanListeners(): void {
    const devices = this.getDiscoveredDevices();
    this.scanListeners.forEach(listener => listener(devices));
  }

  // Listeners for connection changes
  addConnectionListener(listener: (devices: ConnectedDevice[]) => void): void {
    this.connectionListeners.push(listener);
  }

  removeConnectionListener(listener: (devices: ConnectedDevice[]) => void): void {
    const index = this.connectionListeners.indexOf(listener);
    if (index > -1) {
      this.connectionListeners.splice(index, 1);
    }
  }

  private notifyConnectionListeners(): void {
    const devices = this.getConnectedDevices();
    this.connectionListeners.forEach(listener => listener(devices));
  }

  // Utility methods
  getScanningStatus(): boolean {
    return this.isScanning;
  }

  // Cleanup
  destroy(): void {
    this.stopScan();
    this.scanListeners = [];
    this.connectionListeners = [];
    this.discoveredDevices.clear();
    this.connectedDevices.clear();
  }
}

export default BLEService.getInstance();
