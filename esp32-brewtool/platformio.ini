; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env]
platform = espressif32
board = esp32v3
framework = arduino
lib_deps = 
	milesburton/DallasTemperature@^3.11.0
	bblanchon/ArduinoJson@^7.1.0
	h2zero/NimBLE-Arduino@^1.4.2
extra_scripts = pre:set_ble_vars.py
board_build.embed_txtfiles = gaerschrank_api_philun_de.pem

[env:release]
