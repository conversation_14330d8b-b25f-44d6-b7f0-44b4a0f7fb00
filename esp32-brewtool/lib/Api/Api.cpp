#include <Http.hpp>

static int _MaxMinusTemperature = -274;

long loadTimeFromApi() {
  String route = "api/v1/brewtool/time";
  String responseTime = getData(route);
  if (responseTime != "") {
    long timestamp = (long) responseTime.toInt();
    return timestamp;
  }
  return 0;
}

int getDestinatedTemperature() {
  String route = "api/v1/brewtool/" + getMac() + "/config";
  String response = getData(route);
  if (response == HTTP_NOT_FOUND || response == "") {
    return _MaxMinusTemperature;
  } else if (response != "") {
     String strTemperature = response.substring(0, response.indexOf(";"));
    const int temperature = (int) strTemperature.toInt();
    return temperature;
  } else {
    return _MaxMinusTemperature;
  }
}

/*
 Meldet die Schaltung der Kühlung oder Heizung an den Server
*/
void sendElectricalData(char* type, bool status) {
  String statusValue = status ? "START" : "STOP";
  sendPost("api/v1/brewtool/" + getMac() + "/electricity", String(type) + ";" + statusValue);
}

void sendTemperatureStatisticToApi(float temperature) {
  sendPost("api/v1/brewtool/" + getMac() + "/temperature", (String) temperature);
}