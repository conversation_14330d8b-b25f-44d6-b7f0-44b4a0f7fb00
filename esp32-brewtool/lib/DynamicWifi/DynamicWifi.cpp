#include <WiFi.h>
#include <Preferences.h>
#include <string.h>

#define PREV_WIFI_LABEL "philun-wifi"

/*
  Constants
*/
char const *LABEL_WIFI_SSID = "ssid";
char const *LABEL_WIFI_PASSWORD = "password";

Preferences preferences;

extern bool hasInternet = false;

long lastReconnect = 0;

bool connectWifi() {
  // TODO: check if wifi is already connected
  // TODO: check if wifi is already connected to the right network
  preferences.begin(PREV_WIFI_LABEL, true);
  const String ssid = preferences.getString(LABEL_WIFI_SSID, "");
  const String password = preferences.getString(LABEL_WIFI_PASSWORD, "");
  preferences.end();

  if (WiFi.SSID() == ssid && WiFi.status() == WL_CONNECTED) {
    return true;
  }

  if (ssid != "" && password != "") {
    WiFi.begin(ssid.c_str(), password.c_str());
    for (int i = 0; i < 10; i++) {
      delay(500);
      if (WiFi.status() == WL_CONNECTED) {
        Serial.println("Connected to " + ssid);
        return true;
      }
    }
  }

  Serial.println("Not connected to " + ssid);
  return false;
};

void setupWifi() {
  WiFi.mode(WIFI_STA);
  WiFi.setAutoReconnect(true);
  hasInternet = connectWifi();
}

void onLoopWifi() {
  hasInternet = WiFi.status() == WL_CONNECTED;

  if (!hasInternet && lastReconnect == 0) {
    Serial.println("No internet connection");
    lastReconnect = millis();
  }

  if (!hasInternet && lastReconnect != 0 && millis() - lastReconnect > 600000) {
    lastReconnect = 0;

    hasInternet = connectWifi();
  }
}

String getWifiPassword() {
  preferences.begin(PREV_WIFI_LABEL, true);
  String password = preferences.getString(LABEL_WIFI_PASSWORD, "");
  preferences.end();
  return password;
};

bool setWifi(String ssid, String password) {
  preferences.begin(PREV_WIFI_LABEL);
  preferences.putString(LABEL_WIFI_SSID, ssid);
  preferences.putString(LABEL_WIFI_PASSWORD, password);
  preferences.end();
  return connectWifi();
};

String getWifiSsid() {
  preferences.begin(PREV_WIFI_LABEL, true);
  String ssid = preferences.getString(LABEL_WIFI_SSID, "");
  preferences.end();
  return ssid;
};