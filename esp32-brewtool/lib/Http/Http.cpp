#include <HTTPClient.h>
#include <DynamicWifi.hpp>
#include <Preferences.h>

static char const *LABEL_API_KEY = "api-key";
static char const *LABEL_SERVER_CERTIFICATE = "server_certificate";

#define HTTP_UNKNOW_ERROR "Unknown error"
#define HTTP_NOT_FOUND "Not found"
#define HTTP_SERVER_ERROR "Server error"

extern const uint8_t server_cert_pem_start[] asm("_binary_gaerschrank_api_philun_de_pem_start");
extern const uint8_t server_cert_pem_end[] asm("_binary_gaerschrank_api_philun_de_pem_end");

const char* PREF_LABEL_HTTP = "http";

void setApiKey(String newApiKey) {
  Preferences httpPreferences;
  httpPreferences.begin(PREF_LABEL_HTTP, false);
  httpPreferences.putString(LABEL_API_KEY, newApiKey);
  httpPreferences.end();
}

String mac2String(byte ar[]) {
  String s;
  for (byte i = 0; i < 6; ++i)
  {
    char buf[3];
    sprintf(buf, "%02X", ar[i]); // J-M-L: slight modification, added the 0 in the format for padding 
    s += buf;
    if (i < 5) s += ':';
  }
  return s;
}

String getMac() {
  return mac2String((byte*) ESP.getEfuseMac());
}

void preHttpRequest(HTTPClient &http, String route) {
  String serverPath = String(API_HOST) + "/" + route;
      
  if (serverPath.startsWith("https")) {
    http.begin(serverPath, (char *)server_cert_pem_start);
  } else {
    http.begin(serverPath);
  }

  Preferences httpPreferences;
  httpPreferences.begin(PREF_LABEL_HTTP, false);
  http.addHeader("X-ApiKey", httpPreferences.getString(LABEL_API_KEY, DEFAULT_API_KEY));
  httpPreferences.end();
  http.addHeader("X-Device", getMac());

  // const char* headers[] = {HTTP_VERSION_HEADER};
  // http.collectHeaders(headers, sizeof(headers)/ sizeof(headers[0]));
}

// void postHttpRequest(HTTPClient &http) {
//   String receivedVersion = http.header(HTTP_VERSION_HEADER);
//   if (receivedVersion != VERSION && receivedVersion != "") {
//     // startFirmwareUpgrade();
//   }
// }

void sendPost(String route, String data) {
  HTTPClient http;

  preHttpRequest(http, route);

  http.addHeader("Content-Type", "application/json");

  // std::unique_ptr<BearSSL::WiFiClientSecure> client(new BearSSL::WiFiClientSecure);
  // client->setInsecure();

  int httpResponseCode = http.POST(data);

  Serial.printf("Request: %s => %s\n", route, data);
  Serial.printf("HTTP Response code: %d\n", httpResponseCode);

  if (httpResponseCode < 0) {
    String response = http.getString();
  } else {
      Serial.printf("Error on sending POST: %s\n", http.errorToString(httpResponseCode).c_str());
  }

  // postHttpRequest(http);
    
  http.end();
}

String getData(String route) {
  HTTPClient http;

  preHttpRequest(http, route);

  int httpResponseCode = http.GET();

  if (httpResponseCode < 0) {
      Serial.println("Error on HTTP request");
      http.end();
      return HTTP_UNKNOW_ERROR; // Ensure this constant is defined
  }

  if (httpResponseCode == 404) {
      Serial.println("404 Not Found");
      http.end();
      return HTTP_NOT_FOUND; // Or create a constant for "not found"
  }

  // Check for server errors
  if (httpResponseCode >= 500) {
      Serial.printf("Server error: %d\n", httpResponseCode);
      http.end();
      return HTTP_SERVER_ERROR; // Define this constant
  }

  String payload = http.getString();
  if (payload.length() == 0) {
      Serial.println("Received empty payload");
      http.end();
      return HTTP_UNKNOW_ERROR;
  }

  // postHttpRequest(http);

  // String payload = http.getString();

  Serial.println("Payload: " + payload);
  
  http.end();

  return payload;
}