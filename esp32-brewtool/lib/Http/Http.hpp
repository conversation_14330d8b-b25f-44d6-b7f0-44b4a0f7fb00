#include <HTTPClient.h>
#include <DynamicWifi.hpp>
#include <Preferences.h>

// extern const char *HTTP_UNKNOW_ERROR;
// extern const char *HTTP_NOT_FOUND;

#define HTTP_UNKNOW_ERROR "Unknown error"
#define HTTP_NOT_FOUND "Not found"
#define HTTP_SERVER_ERROR "Server error"

extern const uint8_t server_cert_pem_start[];
extern const uint8_t server_cert_pem_end[];

void sendPost(String route, String data);

String getData(String route);

void onInitHttp();

void setApiKey(String newApiKey);

String getMac();