import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import useDeviceStatus, { COLD_CRUSH } from 'hooks/Fermentation/useDeviceStatus'
import { Brewtool } from 'services/BrewtoolService'
import { DeviceType } from 'services/DeviceService'

import SpindelDisplay from 'components/Fermentation/SpindelDisplay'
import StatisticsSection from 'components/Fermentation/StatisticsSection'
import StatusBadge from 'components/Fermentation/StatusBadge'
import TemperatureDisplay from 'components/Fermentation/TemperatureDisplay'

type DeviceDetailProps = {
  device: Brewtool
  onStartFermentation: () => void
  onColdCrash: () => void
  onTurnOff: () => void
  onPlatoAlert: () => void
  onConnectSpindle: () => void
}

const DeviceDetail = ({
  device,
  onStartFermentation,
  onColdCrash,
  onTurnOff,
  onPlatoAlert,
  onConnectSpindle,
}: DeviceDetailProps) => {
  const { t } = useTranslation()

  const spindle = useMemo(
    () => device.deviceConnections.find(d => d.type === DeviceType.ISPINDEL),
    [device.deviceConnections]
  )

  const status = useDeviceStatus(device)

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow">
      {/* Header */}
      <div className="border-b p-4 md:p-6">
        <div className="flex flex-wrap items-center justify-between gap-2">
          <h1 className="text-xl font-bold text-gray-800 md:text-2xl">
            {device.name || t('fermentation.unnamedDevice')}
          </h1>
          <StatusBadge status={status} large />
        </div>
      </div>
      {/* Main content */}
      <div className="p-4 md:p-6">
        {/* Temperature section */}
        <div className="mb-6">
          <TemperatureDisplay brewtool={device} />
        </div>

        {/* Spindle information - only shown if present */}
        <SpindelDisplay spindle={spindle} onConnectSpindle={onConnectSpindle} />

        {/* Action buttons */}
        <div className="mb-6 flex flex-wrap gap-3">
          {!device.config?.temperature && (
            <button
              onClick={onStartFermentation}
              className="rounded-lg bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700"
            >
              {t('fermentation.startFermentation')}
            </button>
          )}
          {device.config?.temperature && device.config.temperature !== COLD_CRUSH && (
            <button
              onClick={onColdCrash}
              className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
            >
              {t('fermentation.coldCrash')}
            </button>
          )}
          {device.config?.temperature && (
            <button
              onClick={onTurnOff}
              className="rounded-lg bg-gray-600 px-4 py-2 text-white transition-colors hover:bg-gray-700"
            >
              {t('fermentation.turnOff')}
            </button>
          )}
          {/* Plato alert button - only shown during fermentation and if spindle is present */}
          {device.config?.temperature && spindle && (
            <button
              onClick={onPlatoAlert}
              className="rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-100"
            >
              {t('fermentation.setPlatoAlert')}
            </button>
          )}
        </div>
        {/* Statistics section */}
        {device.config?.temperature && <StatisticsSection device={device} spindle={spindle} />}
      </div>
    </div>
  )
}
export default DeviceDetail
